%% CSTR Model Predictive Control Example
% This script demonstrates the capabilities of MATLAB's MPC toolbox
% using a Continuous Stirred-Tank Reactor (CSTR) with an exothermic reaction
%
% Process: A -> B (exothermic reaction)
% Control objective: Maintain product concentration and temperature
% Manipulated variables: Coolant flow rate and feed flow rate
% Measured outputs: Product concentration and reactor temperature

clear all; close all; clc;

%% Plant Parameters
% Reactor and reaction parameters
V = 100;        % Reactor volume (L)
k0 = 7.2e10;    % Pre-exponential factor (1/min)
E = 8750;       % Activation energy (K)
deltaH = -5e4;  % Heat of reaction (cal/mol)
rho = 1000;     % Density (g/L)
Cp = 1;         % Heat capacity (cal/g/K)
UA = 5e4;       % Heat transfer coefficient (cal/min/K)
Caf = 1;        % Feed concentration (mol/L)
Tf = 350;       % Feed temperature (K)

% Nominal operating point
Ca_ss = 0.5;    % Steady-state concentration (mol/L)
T_ss = 350;     % Steady-state temperature (K)
Fc_ss = 15;     % Steady-state coolant flow (L/min)
F_ss = 100;     % Steady-state feed flow (L/min)
Tc = 300;       % Coolant temperature (K)

%% Define Nonlinear CSTR Model
% State variables: x = [Ca; T] (concentration and temperature)
% Inputs: u = [Fc; F] (coolant flow and feed flow)

% Nonlinear ODE function
cstr_ode = @(t, x, u, Tf_current) [
    (u(2)/V)*(Caf - x(1)) - k0*exp(-E/x(2))*x(1);  % dCa/dt
    (u(2)/V)*(Tf_current - x(2)) - (deltaH/(rho*Cp))*k0*exp(-E/x(2))*x(1) + ...
    (UA/(V*rho*Cp))*(Tc - x(2))*(1 + u(1)/Fc_ss)   % dT/dt
];

%% Linearize the Plant at Operating Point
% Create symbolic variables for linearization
syms Ca T Fc F real

% Define state and input vectors
x_sym = [Ca; T];
u_sym = [Fc; F];

% Define the nonlinear dynamics symbolically
f1 = (F/V)*(Caf - Ca) - k0*exp(-E/T)*Ca;
f2 = (F/V)*(Tf - T) - (deltaH/(rho*Cp))*k0*exp(-E/T)*Ca + ...
     (UA/(V*rho*Cp))*(Tc - T)*(1 + Fc/Fc_ss);
f = [f1; f2];

% Calculate Jacobians
A_sym = jacobian(f, x_sym);
B_sym = jacobian(f, u_sym);

% Evaluate at operating point
A = double(subs(A_sym, [Ca, T, Fc, F], [Ca_ss, T_ss, Fc_ss, F_ss]));
B = double(subs(B_sym, [Ca, T, Fc, F], [Ca_ss, T_ss, Fc_ss, F_ss]));
C = eye(2);  % Measure both states
D = zeros(2, 2);

% Create continuous-time state-space model
Ts = 0.5;  % Sample time (min)
plant_c = ss(A, B, C, D);
plant_c.InputName = {'Coolant Flow', 'Feed Flow'};
plant_c.OutputName = {'Concentration', 'Temperature'};
plant_c.StateName = {'Ca', 'T'};

% Discretize the plant
plant_d = c2d(plant_c, Ts);

%% Design Model Predictive Controller
% Create MPC object
mpcobj = mpc(plant_d, Ts);

% Set prediction and control horizons
mpcobj.PredictionHorizon = 20;
mpcobj.ControlHorizon = 5;

% Set MPC parameters
mpcobj.PredictionHorizon = 10;    % Shorter prediction horizon
mpcobj.ControlHorizon = 3;        % Shorter control horizon

% Set constraints with very relaxed bounds
% Input constraints (manipulated variables)
mpcobj.MV(1).Min = 0;      % Minimum coolant flow
mpcobj.MV(1).Max = 100;    % Maximum coolant flow (very relaxed)
mpcobj.MV(2).Min = 10;     % Minimum feed flow (very relaxed)
mpcobj.MV(2).Max = 300;    % Maximum feed flow (very relaxed)

% Rate constraints (very relaxed)
mpcobj.MV(1).RateMin = -50;
mpcobj.MV(1).RateMax = 50;
mpcobj.MV(2).RateMin = -50;
mpcobj.MV(2).RateMax = 50;

% Output constraints (very relaxed)
mpcobj.OV(1).Min = 0.1;    % Minimum concentration
mpcobj.OV(1).Max = 0.9;    % Maximum concentration
mpcobj.OV(2).Min = 320;    % Minimum temperature
mpcobj.OV(2).Max = 380;    % Maximum temperature

% Set weights (tuning parameters)
mpcobj.Weights.MV = [0.01, 0.01];      % Very small input weights
mpcobj.Weights.MVRate = [0.05, 0.05];  % Very small rate weights
mpcobj.Weights.OV = [1, 1];            % Equal output weights
mpcobj.Weights.ECR = 100;              % Much smaller constraint softening weight

%% Simulate Closed-Loop System
% Simulation parameters
Tf_sim = 50;  % Simulation time (min)
t_sim = 0:Ts:Tf_sim;
N = length(t_sim);

% Initialize arrays
x = zeros(2, N);
u = zeros(2, N);
y = zeros(2, N);
ref = zeros(2, N);

% Initial conditions
x(:,1) = [Ca_ss; T_ss];
u(:,1) = [Fc_ss; F_ss];

% Define reference trajectory with setpoint changes
ref(1,:) = Ca_ss * ones(1,N);
ref(2,:) = T_ss * ones(1,N);
ref(1, floor(N/4):floor(N/2)) = 0.55;  % Step change in concentration setpoint
ref(2, floor(N/2):floor(3*N/4)) = 355;  % Step change in temperature setpoint

% Add a disturbance (feed temperature change)
Tf_dist = Tf * ones(1,N);
Tf_dist(floor(3*N/4):end) = 340;  % Feed temperature drops at 75% of simulation time

% Create state estimator (Kalman filter)
[kest, L, P] = kalman(plant_d, 0.01*eye(2), 0.001*eye(2));

% Initialize estimator state and MPC state
xest = x(:,1);
mpcstate = mpcstate(mpcobj);

% Simulation loop
fprintf('Running MPC simulation...\n');
for k = 1:N-1
    % Current time
    t = t_sim(k);
    
    % Measure outputs (add small measurement noise)
    y(:,k) = x(:,k) + 0.001*randn(2,1);
    
    % Update state estimate
    xest = A*xest + B*u(:,k) + L*(y(:,k) - C*xest);
    
    % Calculate optimal control using MPC
    [u_mpc, info] = mpcmove(mpcobj, mpcstate, y(:,k), ref(:,k));
    
    % Apply control to nonlinear plant
    u(:,k+1) = u_mpc;
    
    % Simulate nonlinear plant (using ode45 for accuracy)
    [~, x_ode] = ode45(@(t,x) cstr_ode(t, x, u(:,k+1), Tf_dist(k)), ...
                       [0 Ts], x(:,k));
    x(:,k+1) = x_ode(end,:)';
    
    % Display progress
    if mod(k, 20) == 0
        fprintf('Time: %.1f min (%.0f%% complete)\n', t, 100*k/N);
    end
end
y(:,N) = x(:,N);

%% Visualization
fprintf('Creating plots...\n');

% Create figure with subplots
figure('Position', [100, 100, 1200, 800]);

% Ensure all vectors have the same length
t_plot = t_sim(1:length(y));
ref_plot = ref(:,1:length(y));

% Plot 1: Concentration
subplot(2,2,1);
plot(t_plot, y(1,:), 'b-', 'LineWidth', 2);
hold on;
plot(t_plot, ref_plot(1,:), 'r--', 'LineWidth', 1.5);
plot(t_plot, mpcobj.OV(1).Min*ones(size(t_plot)), 'k:', 'LineWidth', 1);
plot(t_plot, mpcobj.OV(1).Max*ones(size(t_plot)), 'k:', 'LineWidth', 1);
xlabel('Time (min)');
ylabel('Concentration (mol/L)');
title('Product Concentration Control');
legend('Actual', 'Setpoint', 'Constraints', 'Location', 'best');
grid on;

% Plot 2: Temperature
subplot(2,2,2);
plot(t_plot, y(2,:), 'b-', 'LineWidth', 2);
hold on;
plot(t_plot, ref_plot(2,:), 'r--', 'LineWidth', 1.5);
plot(t_plot, mpcobj.OV(2).Min*ones(size(t_plot)), 'k:', 'LineWidth', 1);
plot(t_plot, mpcobj.OV(2).Max*ones(size(t_plot)), 'k:', 'LineWidth', 1);
xlabel('Time (min)');
ylabel('Temperature (K)');
title('Reactor Temperature Control');
legend('Actual', 'Setpoint', 'Constraints', 'Location', 'best');
grid on;

% Plot 3: Manipulated Variables
subplot(2,2,3);
u_plot = u(:,1:length(y));
stairs(t_plot, u_plot(1,:), 'g-', 'LineWidth', 2);
hold on;
stairs(t_plot, u_plot(2,:), 'm-', 'LineWidth', 2);
xlabel('Time (min)');
ylabel('Flow Rate (L/min)');
title('Manipulated Variables');
legend('Coolant Flow', 'Feed Flow', 'Location', 'best');
grid on;

% Plot 4: Phase Portrait
subplot(2,2,4);
plot(y(1,:), y(2,:), 'b-', 'LineWidth', 2);
hold on;
plot(Ca_ss, T_ss, 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r');
plot(0.55, T_ss, 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g');
plot(Ca_ss, 355, 'mo', 'MarkerSize', 8, 'MarkerFaceColor', 'm');
xlabel('Concentration (mol/L)');
ylabel('Temperature (K)');
title('Phase Portrait (Ca vs T)');
legend('Trajectory', 'Initial SS', 'Ca Target', 'T Target', 'Location', 'best');
grid on;

%% Performance Analysis
fprintf('\n=== MPC Performance Analysis ===\n');

% Calculate performance metrics
Ca_error = sqrt(mean((y(1,:) - ref(1,:)).^2));
T_error = sqrt(mean((y(2,:) - ref(2,:)).^2));
fprintf('RMS Tracking Error:\n');
fprintf('  Concentration: %.4f mol/L\n', Ca_error);
fprintf('  Temperature: %.2f K\n', T_error);

% Control effort
control_effort = sum(sum(diff(u, 1, 2).^2));
fprintf('\nTotal Control Effort: %.2f\n', control_effort);

% Constraint violations
Ca_violations = sum(y(1,:) < mpcobj.OV(1).Min) + sum(y(1,:) > mpcobj.OV(1).Max);
T_violations = sum(y(2,:) < mpcobj.OV(2).Min) + sum(y(2,:) > mpcobj.OV(2).Max);
fprintf('\nConstraint Violations:\n');
fprintf('  Concentration: %d samples\n', Ca_violations);
fprintf('  Temperature: %d samples\n', T_violations);

%% Advanced MPC Features Demonstration
fprintf('\n=== Advanced MPC Features ===\n');

% 1. Economic MPC - Minimize feed consumption while maintaining quality
figure('Position', [100, 100, 800, 600]);
u_plot = u(:,1:length(y));
economic_cost = cumsum(u_plot(2,:)) * Ts;  % Cumulative feed usage
subplot(2,1,1);
plot(t_plot, economic_cost, 'b-', 'LineWidth', 2);
xlabel('Time (min)');
ylabel('Cumulative Feed (L)');
title('Economic Performance - Total Feed Consumption');
grid on;

% 2. Show final steady state values
subplot(2,1,2);
bar([Ca_ss, y(1,end); T_ss, y(2,end)]);
set(gca, 'XTickLabel', {'Concentration', 'Temperature'});
legend('Initial SS', 'Final SS', 'Location', 'best');
title('Initial vs Final Steady State');
grid on;

fprintf('\nMPC demonstration complete!\n');
fprintf('This example showcased:\n');
fprintf('- Nonlinear CSTR control with linearized MPC\n');
fprintf('- Multiple input/output constraints\n');
fprintf('- Setpoint tracking and disturbance rejection\n');
fprintf('- State estimation with Kalman filter\n');
fprintf('- Performance metrics and visualization\n');